# Frontend Integration Guide: TikTok-Style Engagement Tracking

This document explains how the TikTok-style engagement algorithm has been integrated into the Flutter frontend.

## Overview

The engagement tracking system automatically measures user interactions with posts to feed the backend algorithm that powers personalized content ranking.

## What's Been Added

### 1. EngagementTrackingService (`lib/services/engagement_tracking_service.dart`)

A comprehensive service that handles:
- **View Duration Tracking**: Measures how long users view each post
- **Scroll Position Tracking**: Records how much of a post users scroll through
- **Interaction Detection**: Tracks likes, comments, reactions, and reflexes
- **Backend Communication**: Sends tracking data to the engagement API
- **New Relic Analytics**: Records events for monitoring and analysis

### 2. Automatic View Tracking in FeedItem

The `FeedItem` widget now automatically:
- **Starts tracking** when a post becomes visible
- **Ends tracking** when a post becomes invisible or widget is disposed
- **Handles post changes** by ending tracking for old posts and starting for new ones

### 3. Interaction Tracking in Feed Overlays

All user interactions are automatically tracked:
- **Likes**: Tracked when users tap the like button
- **Comments**: Tracked when users navigate to comment on posts
- **Reactions**: Tracked when users add emoji reactions
- **Reflexes**: Tracked when users create reflexes

### 4. App Lifecycle Management

The system handles app state changes:
- **Background/Foreground**: Ends all tracking when app goes to background
- **App Termination**: Cleans up tracking sessions properly
- **Memory Management**: Prevents memory leaks from active tracking sessions

## API Integration

### New Backend Endpoints Used

1. **POST `/posts/{id}/view`** - Tracks post views
   ```json
   {
     "viewDuration": 5000,
     "scrollPosition": 0.8,
     "interacted": true
   }
   ```

2. **GET `/posts/engagement-feed`** - Gets TikTok-style ranked feed
   ```json
   {
     "posts": [...],
     "algorithm": "engagement-based",
     "hasMore": true
   }
   ```

## How It Works

### View Tracking Flow

1. **Post Becomes Visible**: `EngagementTrackingService.startViewTracking()` called
2. **User Scrolls**: Scroll position automatically tracked
3. **User Interacts**: Interactions marked via `markInteraction()`
4. **Post Becomes Invisible**: `endViewTracking()` sends data to backend
5. **Backend Processing**: Engagement metrics updated asynchronously

### Data Collected

- **View Duration**: Milliseconds spent viewing the post
- **Scroll Position**: Maximum scroll depth (0.0 to 1.0)
- **Interactions**: Types of interactions (like, comment, reaction, reflex)
- **Device Type**: Mobile/desktop detection
- **Timestamp**: When the view occurred

### Privacy & Performance

- **Minimal Data**: Only engagement metrics, no personal content
- **Batched Requests**: Views sent individually but efficiently
- **Error Handling**: Graceful degradation if tracking fails
- **Background Processing**: Doesn't block UI interactions

## Usage Examples

### Manual Tracking (if needed)

```dart
// Start tracking a post view
EngagementTrackingService.instance.startViewTracking(post);

// Mark user interaction
EngagementTrackingService.instance.markInteraction(postId, 'like');

// Update scroll position
EngagementTrackingService.instance.updateScrollPosition(postId, 0.75);

// End tracking
EngagementTrackingService.instance.endViewTracking(postId);
```

### Getting Engagement-Based Feed

```dart
// Get TikTok-style ranked posts
final posts = await AwsPostsService.instance.getEngagementBasedFeed(limit: 20);
```

## New Relic Analytics

All engagement events are automatically sent to New Relic:

- `post_view_started` - When user starts viewing a post
- `post_view_ended` - When user stops viewing a post
- `post_interaction` - When user interacts with a post
- `engagement_feed_loaded` - When engagement feed is loaded

## Configuration

No additional configuration required. The system:
- ✅ **Auto-detects** when posts are visible
- ✅ **Auto-tracks** all user interactions
- ✅ **Auto-sends** data to backend
- ✅ **Auto-handles** app lifecycle events

## Testing

To verify the integration is working:

1. **Check Logs**: Look for `EngagementTracking:` messages in debug logs
2. **Monitor Network**: Watch for POST requests to `/posts/{id}/view`
3. **New Relic**: Check for engagement events in New Relic dashboard
4. **Backend Logs**: Verify view tracking data is being received

## Performance Impact

- **Minimal CPU**: Lightweight tracking with efficient timers
- **Low Memory**: Small tracking objects, automatic cleanup
- **Network Efficient**: Only sends meaningful view data (>100ms views)
- **Battery Friendly**: No continuous polling, event-driven tracking

## Future Enhancements

The current implementation provides a solid foundation for:

1. **Advanced Metrics**: Eye tracking, attention heatmaps
2. **Real-time Updates**: Live engagement score updates
3. **A/B Testing**: Different algorithm parameters
4. **Machine Learning**: User behavior prediction
5. **Content Optimization**: Creator insights and recommendations

## Troubleshooting

### Common Issues

1. **Views Not Tracked**: Check if user is authenticated
2. **Missing Interactions**: Verify overlay components are using tracking
3. **Memory Leaks**: Ensure proper disposal in widget lifecycle
4. **Network Errors**: Check API connectivity and authentication

### Debug Commands

```dart
// Enable verbose logging
AppLogger.debug('EngagementTracking: Debug info here');

// Check active sessions
print('Active sessions: ${EngagementTrackingService.instance._activeSessions.length}');
```

## Summary

The TikTok-style engagement tracking is now fully integrated and working automatically. The system:

- ✅ **Tracks all user interactions** with posts
- ✅ **Measures view duration and scroll depth**
- ✅ **Sends data to backend algorithm**
- ✅ **Handles app lifecycle properly**
- ✅ **Provides analytics via New Relic**
- ✅ **Works transparently** without affecting user experience

The engagement algorithm will now learn from user behavior and provide increasingly personalized content recommendations, similar to TikTok's "For You" page.
