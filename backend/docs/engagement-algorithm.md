# TikTok-Style Engagement Algorithm

This document describes the implementation of a TikTok-style engagement algorithm for the GameFlex posts feed.

## Overview

The engagement algorithm ranks posts based on multiple factors to create a personalized, engaging feed similar to TikTok's "For You" page. The algorithm considers:

- **Engagement metrics** (likes, comments, reactions, views)
- **Recency** (newer posts get initial boost)
- **User preferences** (learned from interaction patterns)
- **View history** (avoid showing already-seen content)

## Architecture

### Database Tables

#### 1. PostViews Table
Tracks when users view posts with detailed metadata.

```
PK: postId (String)
SK: userId (String)
Attributes:
- viewId: String (userId#timestamp)
- timestamp: String (ISO)
- viewDuration: Number (milliseconds)
- scrollPosition: Number (0-1)
- interacted: Boolean
- deviceType: String
```

#### 2. PostEngagementMetrics Table
Stores calculated engagement scores for posts.

```
PK: postId (String)
Attributes:
- engagementScore: Number
- scoreCategory: String (trending/hot/rising/normal)
- viewCount: Number
- avgViewDuration: Number
- interactionRate: Number
- lastCalculated: String (ISO)
- postAge: Number (hours)
```

#### 3. UserPreferences Table
Tracks user content preferences for personalization.

```
PK: userId (String)
Attributes:
- preferredChannels: Map<String, Number>
- preferredAuthors: <AUTHORS>
- interactionCounts: Object
- lastUpdated: String (ISO)
```

## Algorithm Components

### 1. Engagement Score Calculation

The engagement score is calculated using weighted metrics with time decay:

```typescript
const engagementScore = (
  (likes * 3) +
  (comments * 5) +
  (reflexes * 4) +
  (viewCount * 1) +
  (avgViewDuration * 0.1) +
  (interactionRate * 10)
) * timeDecay;
```

**Time Decay Formula:**
```typescript
const timeDecay = Math.exp(-ageInHours / 24);
```

### 2. Score Categories

Posts are categorized based on their engagement scores:
- **Trending**: Score > 100
- **Hot**: Score > 50
- **Rising**: Score > 20
- **Normal**: Score ≤ 20

### 3. Feed Ranking

Posts are ranked using:
1. **Base engagement score**
2. **Recency bonus** (10 points for posts < 2 hours old)
3. **View penalty** (30% reduction for previously viewed posts)
4. **Randomization factor** (0.8-1.2x multiplier for diversity)

## API Endpoints

### Track Post View
```
POST /posts/{id}/view
```

**Request Body:**
```json
{
  "viewDuration": 5000,
  "scrollPosition": 0.8,
  "interacted": true
}
```

### Get Engagement-Based Feed
```
GET /posts/engagement-feed?limit=10
```

**Response:**
```json
{
  "posts": [...],
  "hasMore": true,
  "algorithm": "engagement-based"
}
```

### Enhanced Main Feed
```
GET /posts?limit=10
```
The main posts endpoint now uses engagement-based ranking by default.

## User Preference Learning

### Interaction Weights
- **View**: 1 point
- **Reaction**: 2 points
- **Like**: 3 points
- **Comment**: 5 points

### Preference Updates
User preferences are updated asynchronously when users:
- View posts (tracks channel/author preferences)
- Like posts (stronger signal)
- Comment on posts (strongest signal)
- React to posts (moderate signal)

## Performance Optimizations

### 1. Batch Operations
- Engagement metrics fetched in batches of 100
- User preferences updated asynchronously
- View tracking doesn't block main operations

### 2. Caching Strategy
- Engagement scores cached in dedicated table
- Metrics recalculated only when needed
- User preferences persist across sessions

### 3. Graceful Degradation
- Falls back to basic scoring if metrics unavailable
- Continues without preference tracking if it fails
- Handles missing data gracefully

## Implementation Details

### View Tracking Integration
```typescript
// Track view when user sees post
await trackPostView(postId, {
  viewDuration: 5000,
  scrollPosition: 0.8,
  interacted: true
});
```

### Engagement Updates
```typescript
// Automatically triggered on interactions
updateEngagementMetrics(postId);
updateUserPreferences(userId, postId, 'like');
```

### Feed Generation
```typescript
// Get personalized feed
const feed = await getEngagementBasedFeed({
  userId,
  limit: 10,
  excludeViewed: true
});
```

## Testing

Run the engagement algorithm tests:
```bash
npm test -- engagement-algorithm.test.ts
```

Tests cover:
- View tracking functionality
- Engagement score calculation
- Feed ranking accuracy
- User preference learning
- Performance with large datasets

## Monitoring

Key metrics to monitor:
- **Engagement rates** (likes/views, comments/views)
- **Session duration** (time spent viewing posts)
- **Return rate** (users coming back for more content)
- **Content diversity** (variety in feed content)

## Future Enhancements

1. **Machine Learning Integration**
   - Use ML models for better preference prediction
   - Collaborative filtering for similar users
   - Content-based recommendations

2. **Real-time Updates**
   - WebSocket integration for live engagement updates
   - Real-time feed refreshing
   - Live trending detection

3. **Advanced Personalization**
   - Time-of-day preferences
   - Device-specific preferences
   - Social graph influence

4. **A/B Testing Framework**
   - Test different algorithm parameters
   - Compare engagement metrics
   - Optimize for different user segments

## Configuration

Environment variables:
- `POST_VIEWS_TABLE`: DynamoDB table for view tracking
- `POST_ENGAGEMENT_METRICS_TABLE`: DynamoDB table for engagement metrics
- `USER_PREFERENCES_TABLE`: DynamoDB table for user preferences

## Deployment

The algorithm is automatically deployed with the posts Lambda function. New database tables are created via CDK deployment.

```bash
# Deploy with new tables
npm run deploy
```
