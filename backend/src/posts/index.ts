import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, Scan<PERSON>ommand, GetCommand, PutCommand, UpdateCommand, DeleteCommand, QueryCommand, BatchGetCommand } from '@aws-sdk/lib-dynamodb';
import { S3Client } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

// Configure AWS SDK v3 clients
const dynamoClient = new DynamoDBClient({
    region: process.env.AWS_REGION || 'us-east-1'
});

const dynamodb = DynamoDBDocumentClient.from(dynamoClient);
const s3 = new S3Client({
    region: process.env.AWS_REGION || 'us-east-1'
});

const POSTS_TABLE = process.env.POSTS_TABLE;
const MEDIA_TABLE = process.env.MEDIA_TABLE;
const COMMENTS_TABLE = process.env.COMMENTS_TABLE;
const LIKES_TABLE = process.env.LIKES_TABLE;
const USERS_TABLE = process.env.USERS_TABLE;
const USER_PROFILES_TABLE = process.env.USER_PROFILES_TABLE;
const CHANNELS_TABLE = process.env.CHANNELS_TABLE;
const FOLLOWS_TABLE = process.env.FOLLOWS_TABLE;
const CHANNEL_MEMBERS_TABLE = process.env.CHANNEL_MEMBERS_TABLE;
const MEDIA_BUCKET = process.env.MEDIA_BUCKET;
import { reactToPost, unreactToPost } from './reactions';
const POST_REACTIONS_TABLE = process.env.POST_REACTIONS_TABLE;
const POST_VIEWS_TABLE = process.env.POST_VIEWS_TABLE;
const POST_ENGAGEMENT_METRICS_TABLE = process.env.POST_ENGAGEMENT_METRICS_TABLE;
const USER_PREFERENCES_TABLE = process.env.USER_PREFERENCES_TABLE;

// TypeScript interfaces
interface Post {
    id: string;
    title?: string;
    content: string;
    mediaId?: string;
    authorId: string;
    userId: string;
    likes: number;
    comments: number;
    reflexes: number;
    status: 'draft' | 'uploading_media' | 'published';
    active: boolean;
    createdAt: string;
    updatedAt: string;
    media?: MediaItem;
    isLikedByCurrentUser?: boolean;
    reactions?: Record<string, number>;
    currentUserReactions?: string[];
}

interface MediaItem {
    id: string;
    status: string;
    [key: string]: any;
}

interface Comment {
    id: string;
    post_id: string;
    user_id: string;
    content: string;
    like_count: number;
    is_active: boolean;
    created_at: string;
    updatedAt: string;
    username?: string;
    displayName?: string;
    avatarUrl?: string;
}

interface User {
    id: string;
    username: string;
    displayName: string;
    avatarUrl?: string;
}

interface Like {
    postId: string;
    userId: string;
    createdAt: string;
}

interface Reaction {
    postId: string;
    reactionKey: string; // `${emoji}#${userId}`
    emoji: string;
    userId: string;
    createdAt: string;
}

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Get posts from followed users and channels
const getFollowedPosts = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        // Get current user ID from authorizer context
        const currentUserId = getUserIdFromContext(event);

        if (!currentUserId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get pagination parameters
        const { limit = '20', offset = '0' } = event.queryStringParameters || {};
        const limitNum = parseInt(limit);
        const offsetNum = parseInt(offset);

        // Get users that the current user follows
        const followsQueryCommand = new QueryCommand({
            TableName: FOLLOWS_TABLE,
            KeyConditionExpression: 'followerId = :followerId',
            ExpressionAttributeValues: {
                ':followerId': currentUserId
            }
        });
        const followsResult = await dynamodb.send(followsQueryCommand);
        const followedUserIds = (followsResult.Items || []).map((follow: any) => follow.followingId);

        // Get channels that the current user is a member of
        const channelMembersQueryCommand = new QueryCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            IndexName: 'userId-index',
            KeyConditionExpression: 'userId = :userId',
            ExpressionAttributeValues: {
                ':userId': currentUserId
            }
        });
        const channelMembersResult = await dynamodb.send(channelMembersQueryCommand);
        const followedChannelIds = (channelMembersResult.Items || []).map((member: any) => member.channelId);

        // If user doesn't follow anyone and isn't in any channels, return empty result
        if (followedUserIds.length === 0 && followedChannelIds.length === 0) {
            return createResponse(200, {
                posts: [],
                count: 0
            });
        }

        // Get all posts and filter by followed users/channels (optimized scan)
        // TODO: Replace with GSI queries once userId-createdAt-index and channelId-createdAt-index are added
        const scanCommand = new ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: '#status = :status AND #active = :active',
            ExpressionAttributeNames: {
                '#status': 'status',
                '#active': 'active'
            },
            ExpressionAttributeValues: {
                ':status': 'published',
                ':active': true
            },
            ProjectionExpression: 'id, userId, #status, #active, createdAt, title, description, content, mediaId, mediaId, channelId, likes, comments, reflexes'
        });
        const result = await dynamodb.send(scanCommand);

        // Filter posts by followed users and channels
        const followedPosts = (result.Items || []).filter((post: any) => {
            // Include posts from followed users
            if (followedUserIds.includes(post.userId)) {
                return true;
            }

            // Include posts from followed channels
            if (post.channelId && followedChannelIds.includes(post.channelId)) {
                return true;
            }

            return false;
        });

        // Sort posts by createdAt descending
        const sortedPosts = followedPosts.sort((a: any, b: any) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );

        // Apply pagination
        const paginatedPosts = sortedPosts.slice(offsetNum, offsetNum + limitNum);

        // Enrich posts with related data using optimized batch operations
        const postsWithEnrichedData = await enrichPostsDataOptimized(paginatedPosts, currentUserId);

        return createResponse(200, {
            posts: postsWithEnrichedData,
            count: postsWithEnrichedData.length,
            hasMore: offsetNum + limitNum < sortedPosts.length
        });

    } catch (error) {
        console.error('GetFollowedPosts error:', error);
        return createResponse(500, { error: 'Failed to get followed posts', details: (error as Error).message });
    }
};

// Highly optimized function to enrich posts with related data using DynamoDB efficiently
const enrichPostsDataOptimized = async (posts: any[], currentUserId: string | null): Promise<any[]> => {
    if (posts.length === 0) return posts;

    // Collect all unique IDs for batch operations
    const userIds = [...new Set(posts.map(post => post.userId).filter(Boolean))];
    const channelIds = [...new Set(posts.map(post => post.channelId).filter(Boolean))];
    const mediaIds = [...new Set(posts.map(post => post.mediaId || post.mediaId).filter(Boolean))];
    const postIds = posts.map(post => post.id);

    // Batch fetch users
    const usersMap = new Map();
    if (userIds.length > 0) {
        try {
            // DynamoDB BatchGetItem has a limit of 100 items, so we need to chunk
            const userChunks = chunkArray(userIds, 100);
            for (const chunk of userChunks) {
                const batchGetUsers = new BatchGetCommand({
                    RequestItems: {
                        [USERS_TABLE as string]: {
                            Keys: chunk.map(id => ({ id })),
                            ProjectionExpression: 'id, username, displayName, avatarUrl' // Only fetch needed attributes
                        }
                    }
                });
                const usersResult = await dynamodb.send(batchGetUsers);
                const usersTableName = USERS_TABLE as string;
                if (usersResult.Responses?.[usersTableName]) {
                    usersResult.Responses[usersTableName].forEach((user: any) => {
                        usersMap.set(user.id, user);
                    });
                }
            }
        } catch (error) {
            console.error('Failed to batch fetch users:', error);
        }
    }

    // Batch fetch channels
    const channelsMap = new Map();
    if (channelIds.length > 0) {
        try {
            const channelChunks = chunkArray(channelIds, 100);
            for (const chunk of channelChunks) {
                const batchGetChannels = new BatchGetCommand({
                    RequestItems: {
                        [CHANNELS_TABLE as string]: {
                            Keys: chunk.map(id => ({ id })),
                            ProjectionExpression: 'id, #name, isPublic', // Only fetch needed attributes
                            ExpressionAttributeNames: { '#name': 'name' } // 'name' is a reserved word
                        }
                    }
                });
                const channelsResult = await dynamodb.send(batchGetChannels);
                const channelsTableName = CHANNELS_TABLE as string;
                if (channelsResult.Responses?.[channelsTableName]) {
                    channelsResult.Responses[channelsTableName].forEach((channel: any) => {
                        channelsMap.set(channel.id, channel);
                    });
                }
            }
        } catch (error) {
            console.error('Failed to batch fetch channels:', error);
        }
    }

    // Batch fetch media
    const mediaMap = new Map();
    if (mediaIds.length > 0) {
        try {
            const mediaChunks = chunkArray(mediaIds, 100);
            for (const chunk of mediaChunks) {
                const batchGetMedia = new BatchGetCommand({
                    RequestItems: {
                        [MEDIA_TABLE as string]: {
                            Keys: chunk.map(id => ({ id }))
                        }
                    }
                });
                const mediaResult = await dynamodb.send(batchGetMedia);
                const mediaTableName = MEDIA_TABLE as string;
                if (mediaResult.Responses?.[mediaTableName]) {
                    mediaResult.Responses[mediaTableName].forEach((media: any) => {
                        mediaMap.set(media.id, media);
                    });
                }
            }
        } catch (error) {
            console.error('Failed to batch fetch media:', error);
        }
    }

    // Batch fetch likes for current user
    const likesSet = new Set();
    if (currentUserId && postIds.length > 0) {
        try {
            const likeChunks = chunkArray(postIds, 100);
            for (const chunk of likeChunks) {
                const batchGetLikes = new BatchGetCommand({
                    RequestItems: {
                        [LIKES_TABLE as string]: {
                            Keys: chunk.map(postId => ({ postId, userId: currentUserId })),
                            ProjectionExpression: 'postId' // Only fetch the key to confirm existence
                        }
                    }
                });
                const likesResult = await dynamodb.send(batchGetLikes);
                const likesTableName = LIKES_TABLE as string;
                if (likesResult.Responses?.[likesTableName]) {
                    likesResult.Responses[likesTableName].forEach((like: any) => {
                        likesSet.add(like.postId);
                    });
                }
            }
        } catch (error) {
            console.error('Failed to batch fetch likes:', error);
        }
    }

    // Batch fetch reactions for all posts with controlled concurrency
    const reactionsMap = new Map();
    if (postIds.length > 0) {
        try {
            // Process reactions in smaller batches to avoid overwhelming DynamoDB
            const REACTION_BATCH_SIZE = 5;
            for (let i = 0; i < postIds.length; i += REACTION_BATCH_SIZE) {
                const batch = postIds.slice(i, i + REACTION_BATCH_SIZE);
                const batchPromises = batch.map(async (postId) => {
                    try {
                        const rxQuery = new QueryCommand({
                            TableName: POST_REACTIONS_TABLE,
                            KeyConditionExpression: 'postId = :postId',
                            ExpressionAttributeValues: { ':postId': postId },
                            ProjectionExpression: 'emoji, userId' // Only fetch needed attributes
                        });
                        const rxResult = await dynamodb.send(rxQuery);
                        const counts: Record<string, number> = {};
                        const userEmojis: string[] = [];

                        for (const r of (rxResult.Items || [])) {
                            const emoji = (r as any).emoji;
                            counts[emoji] = (counts[emoji] || 0) + 1;
                            if (currentUserId && (r as any).userId === currentUserId) {
                                userEmojis.push(emoji);
                            }
                        }

                        reactionsMap.set(postId, { counts, userEmojis });
                    } catch (error) {
                        console.error(`Failed to fetch reactions for post ${postId}:`, error);
                        reactionsMap.set(postId, { counts: {}, userEmojis: [] });
                    }
                });

                await Promise.all(batchPromises);
            }
        } catch (error) {
            console.error('Failed to fetch reactions:', error);
        }
    }

    // Enrich posts with fetched data
    return posts.map(post => {
        const enrichedPost = { ...post };

        // Add user data
        const user = usersMap.get(post.userId);
        if (user) {
            enrichedPost.username = user.username;
            enrichedPost.displayName = user.displayName;
            enrichedPost.avatarUrl = user.avatarUrl;
        }

        // Add channel data
        if (post.channelId) {
            const channel = channelsMap.get(post.channelId);
            if (channel) {
                enrichedPost.channelName = channel.name;
                enrichedPost.channelIsPublic = channel.isPublic;
            }
        }

        // Add media data (only URL)
        const mediaId = post.mediaId || post.mediaId;
        if (mediaId) {
            const media = mediaMap.get(mediaId);
            if (media) {
                // Only include essential media information
                enrichedPost.media = {
                    id: media.id,
                    url: media.url || media.final_url,
                    mediaType: media.mediaType,
                    status: media.status
                };
            }
        }

        // Add like status
        enrichedPost.isLikedByCurrentUser = likesSet.has(post.id);

        // Add reactions
        const reactions = reactionsMap.get(post.id);
        if (reactions) {
            enrichedPost.reactions = reactions.counts;
            enrichedPost.currentUserReactions = reactions.userEmojis;
        } else {
            enrichedPost.reactions = {};
            enrichedPost.currentUserReactions = [];
        }

        return enrichedPost;
    });
};

// Helper function to chunk arrays
const chunkArray = <T>(array: T[], size: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
};

// Get all posts using DynamoDB Query (not Scan)
const getPosts = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        // Get current user ID from authorizer context
        const currentUserId = getUserIdFromContext(event);

        // Get pagination parameters
        const { limit = '10', lastEvaluatedKey } = event.queryStringParameters || {};
        const limitNum = parseInt(limit);

        console.log(`getPosts: limit=${limitNum}, lastEvaluatedKey=${lastEvaluatedKey}`);

        // Use optimized scan with better filtering and projection
        // TODO: Replace with GSI query once status-createdAt-index is added
        const scanCommand = new ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: '#status = :status AND #active = :active',
            ExpressionAttributeNames: {
                '#status': 'status',
                '#active': 'active'
            },
            ExpressionAttributeValues: {
                ':status': 'published',
                ':active': true
            },
            ProjectionExpression: 'id, userId, #status, #active, createdAt, title, description, content, mediaId, channelId, likes, comments, reflexes', // Only fetch needed attributes
            Limit: limitNum * 3, // Get more items to account for sorting and pagination
            ExclusiveStartKey: lastEvaluatedKey ? JSON.parse(decodeURIComponent(lastEvaluatedKey)) : undefined
        });

        const result = await dynamodb.send(scanCommand);
        const allPosts = result.Items || [];

        // Get user's viewed posts to optionally deprioritize them
        let viewedPostIds = new Set();
        if (currentUserId) {
            try {
                const viewedPostsQuery = new QueryCommand({
                    TableName: POST_VIEWS_TABLE,
                    IndexName: 'userId-timestamp-index',
                    KeyConditionExpression: 'userId = :userId',
                    ExpressionAttributeValues: { ':userId': currentUserId },
                    ProjectionExpression: 'postId',
                    ScanIndexForward: false,
                    Limit: 500 // Recent viewed posts
                });
                const viewedResult = await dynamodb.send(viewedPostsQuery);
                viewedPostIds = new Set((viewedResult.Items || []).map((item: any) => item.postId));
            } catch (error) {
                console.error('Failed to get viewed posts:', error);
                // Continue without viewed posts filter
            }
        }

        // Get engagement metrics for posts
        const postIds = allPosts.map((post: any) => post.id);
        const engagementMetrics = new Map();

        if (postIds.length > 0) {
            try {
                const batchSize = 100;
                for (let i = 0; i < postIds.length; i += batchSize) {
                    const batch = postIds.slice(i, i + batchSize);
                    const batchGetCommand = new BatchGetCommand({
                        RequestItems: {
                            [POST_ENGAGEMENT_METRICS_TABLE as string]: {
                                Keys: batch.map(postId => ({ postId })),
                                ProjectionExpression: 'postId, engagementScore, scoreCategory'
                            }
                        }
                    });
                    const batchResult = await dynamodb.send(batchGetCommand);
                    const metricsTableName = POST_ENGAGEMENT_METRICS_TABLE as string;
                    if (batchResult.Responses?.[metricsTableName]) {
                        batchResult.Responses[metricsTableName].forEach((metric: any) => {
                            engagementMetrics.set(metric.postId, metric);
                        });
                    }
                }
            } catch (error) {
                console.error('Failed to get engagement metrics:', error);
                // Continue with basic sorting
            }
        }

        // Calculate engagement scores and sort
        const postsWithScores = allPosts.map((post: any) => {
            let metric = engagementMetrics.get(post.id);

            if (!metric) {
                // Calculate basic score for posts without metrics
                const postAge = Date.now() - new Date(post.createdAt).getTime();
                const ageInHours = postAge / (1000 * 60 * 60);
                const timeDecay = Math.exp(-ageInHours / 24);

                const basicScore = (
                    (post.likes || 0) * 3 +
                    (post.comments || 0) * 5 +
                    (post.reflexes || 0) * 4
                ) * timeDecay;

                metric = {
                    engagementScore: basicScore,
                    scoreCategory: basicScore > 20 ? 'rising' : 'normal'
                };
            }

            // Reduce score for viewed posts but don't eliminate them completely
            const viewedPenalty = viewedPostIds.has(post.id) ? 0.3 : 1.0;
            const finalScore = (metric.engagementScore || 0) * viewedPenalty;

            return {
                ...post,
                engagementScore: finalScore,
                scoreCategory: metric.scoreCategory || 'normal'
            };
        });

        // Sort by engagement score with some recency bias
        const sortedPosts = postsWithScores.sort((a: any, b: any) => {
            // Add recency bonus for very new posts (less than 2 hours old)
            const aAge = Date.now() - new Date(a.createdAt).getTime();
            const bAge = Date.now() - new Date(b.createdAt).getTime();
            const aRecencyBonus = aAge < 2 * 60 * 60 * 1000 ? 10 : 0; // 2 hours
            const bRecencyBonus = bAge < 2 * 60 * 60 * 1000 ? 10 : 0;

            const finalScoreA = a.engagementScore + aRecencyBonus;
            const finalScoreB = b.engagementScore + bRecencyBonus;

            return finalScoreB - finalScoreA;
        });

        // Apply pagination after sorting
        const paginatedPosts = sortedPosts.slice(0, limitNum);

        console.log(`getPosts: Found ${allPosts.length} total posts, returning ${paginatedPosts.length} posts with engagement-based ranking`);

        // Enrich posts with related data using batch operations
        const postsWithEnrichedData = await enrichPostsDataOptimized(paginatedPosts, currentUserId);

        return createResponse(200, {
            posts: postsWithEnrichedData,
            count: postsWithEnrichedData.length,
            totalCount: allPosts.length,
            hasMore: paginatedPosts.length < sortedPosts.length
        });

    } catch (error) {
        console.error('GetPosts error:', error);
        return createResponse(500, { error: 'Failed to get posts', details: (error as Error).message });
    }
};

// Get single post
const getPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Get current user ID from authorizer context
        const currentUserId = getUserIdFromContext(event);

        const getPostCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const result = await dynamodb.send(getPostCommand);

        if (!result.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        const post = result.Item as any;

        // Fetch reaction summary and user selections (independent of like check)
        try {
            const rxQuery = new QueryCommand({
                TableName: POST_REACTIONS_TABLE,
                KeyConditionExpression: 'postId = :postId',
                ExpressionAttributeValues: { ':postId': id }
            });
            const rxResult = await dynamodb.send(rxQuery);
            const counts: Record<string, number> = {};
            const userEmojis: string[] = [];
            for (const r of (rxResult.Items || [])) {
                const emoji = (r as any).emoji;
                counts[emoji] = (counts[emoji] || 0) + 1;
                if (currentUserId && (r as any).userId === currentUserId) {
                    userEmojis.push(emoji);
                }
            }
            post.reactions = counts;
            // Note: userEmojis should contain at most 1 emoji due to single reaction enforcement
            post.currentUserReactions = userEmojis;
        } catch (error) {
            console.error(`Failed to fetch reactions for post ${id}:`, error);
        }

        // Fetch media data if post has mediaId
        if (post.mediaId) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: post.mediaId }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (mediaResult.Item) {
                    const media = mediaResult.Item as any;

                    // Use final_url as fallback if url is empty (for backward compatibility)
                    if (!media.url && media.final_url) {
                        media.url = media.final_url;
                    }

                    post.media = media;
                }
            } catch (error) {
                console.error(`Failed to fetch media for post ${post.id}:`, error);
                // Continue without media data if fetch fails
            }
        }

        // Check if current user has liked this post
        let isLikedByCurrentUser = false;
        if (currentUserId) {
            try {
                const getLikeCommand = new GetCommand({
                    TableName: LIKES_TABLE,
                    Key: {
                        postId: post.id,
                        userId: currentUserId
                    }
                });
                const likeResult = await dynamodb.send(getLikeCommand);

                isLikedByCurrentUser = !!likeResult.Item;
            } catch (error) {
                console.error(`Failed to check like status for post ${post.id}:`, error);
                // Continue with false if check fails
            }
        }

        // Add the like status to the post
        post.isLikedByCurrentUser = isLikedByCurrentUser;

        return createResponse(200, { post });

    } catch (error) {
        console.error('GetPost error:', error);
        return createResponse(500, { error: 'Failed to get post', details: (error as Error).message });
    }
};

// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event: APIGatewayProxyEvent): string | null => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return (event.requestContext.authorizer as any).userId;
    }
    return null;
};

// Create draft post (first step of multi-step creation)
const createDraftPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { title, content, channelId } = JSON.parse(event.body);

        console.log('CreateDraftPost: Received channelId:', channelId);

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!content) {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        const postId = uuidv4();
        const post: any = {
            id: postId,
            title: title || null,
            content,
            channelId: channelId || null,
            mediaId: null, // Will be set later if media is uploaded
            authorId: authorId,
            userId: authorId, // Keep for backwards compatibility
            likes: 0,
            comments: 0,
            reflexes: 0,
            status: 'draft', // Status: draft, uploading_media, published
            active: false, // Not active until published
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        const putCommand = new PutCommand({
            TableName: POSTS_TABLE,
            Item: post
        });
        await dynamodb.send(putCommand);

        return createResponse(201, {
            message: 'Draft post created successfully',
            post
        });

    } catch (error) {
        console.error('CreateDraftPost error:', error);
        return createResponse(500, { error: 'Failed to create draft post', details: (error as Error).message });
    }
};

// Create post (legacy endpoint - now creates and publishes immediately)
const createPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { title, content, channelId, mediaId } = JSON.parse(event.body);

        console.log('CreatePost: Received channelId:', channelId);

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!content) {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Support both mediaId and mediaId field names
        const finalMediaId = mediaId;

        // If mediaId is provided, verify it exists
        if (finalMediaId) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: finalMediaId }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (!mediaResult.Item) {
                    return createResponse(400, { error: 'Invalid mediaId: media not found' });
                }
            } catch (error) {
                console.error('Error verifying media:', error);
                return createResponse(400, { error: 'Failed to verify mediaId' });
            }
        }

        const postId = uuidv4();
        const post: any = {
            id: postId,
            title: title || null,
            content,
            channelId: channelId || null,
            mediaId: finalMediaId || null,
            authorId: authorId,
            userId: authorId, // Keep for backwards compatibility
            likes: 0,
            comments: 0,
            reflexes: 0,
            status: 'published', // Status: draft, uploading_media, published
            active: true, // Final switch to show in feeds
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        const putCommand = new PutCommand({
            TableName: POSTS_TABLE,
            Item: post
        });
        await dynamodb.send(putCommand);

        // Fetch media data if mediaId was provided
        if (finalMediaId) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: finalMediaId }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (mediaResult.Item) {
                    post.media = mediaResult.Item;
                }
            } catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }

        return createResponse(201, {
            message: 'Post created successfully',
            post
        });

    } catch (error) {
        console.error('CreatePost error:', error);
        return createResponse(500, { error: 'Failed to create post', details: (error as Error).message });
    }
};

// Update post
const updatePost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { title, content, mediaId } = JSON.parse(event.body);

        // If mediaId is provided, verify it exists
        if (mediaId) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: mediaId }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (!mediaResult.Item) {
                    return createResponse(400, { error: 'Invalid mediaId: media not found' });
                }
            } catch (error) {
                console.error('Error verifying media:', error);
                return createResponse(400, { error: 'Failed to verify mediaId' });
            }
        }

        const updateExpression: string[] = [];
        const expressionAttributeValues: Record<string, any> = {};
        const expressionAttributeNames: Record<string, string> = {};

        if (title !== undefined) {
            updateExpression.push('#title = :title');
            expressionAttributeNames['#title'] = 'title';
            expressionAttributeValues[':title'] = title;
        }

        if (content !== undefined) {
            updateExpression.push('#content = :content');
            expressionAttributeNames['#content'] = 'content';
            expressionAttributeValues[':content'] = content;
        }

        if (mediaId !== undefined) {
            updateExpression.push('mediaId = :mediaId');
            expressionAttributeValues[':mediaId'] = mediaId;
        }

        updateExpression.push('updatedAt = :updatedAt');
        expressionAttributeValues[':updatedAt'] = new Date().toISOString();

        const updateCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: `SET ${updateExpression.join(', ')}`,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues,
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);

        const updatedPost = result.Attributes as any;

        // Fetch media data if post has mediaId
        if (updatedPost.mediaId) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: updatedPost.mediaId }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (mediaResult.Item) {
                    updatedPost.media = mediaResult.Item;
                }
            } catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }

        return createResponse(200, {
            message: 'Post updated successfully',
            post: updatedPost
        });

    } catch (error) {
        console.error('UpdatePost error:', error);
        return createResponse(500, { error: 'Failed to update post', details: (error as Error).message });
    }
};

// Attach media to draft post
const attachMediaToPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { mediaId } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!mediaId) {
            return createResponse(400, { error: 'mediaId is required' });
        }

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Verify media exists and is uploaded
        const getMediaCommand = new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id: mediaId }
        });
        const mediaResult = await dynamodb.send(getMediaCommand);

        if (!mediaResult.Item) {
            return createResponse(400, { error: 'Invalid mediaId: media not found' });
        }

        // Check if media is approved and ready to be attached
        if (mediaResult.Item.status !== 'approved') {
            return createResponse(400, {
                error: 'Media upload not completed',
                details: `Media status is '${mediaResult.Item.status}'. Media must be approved before attaching to posts.`
            });
        }

        // Get the post and verify ownership
        const getPostCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const postResult = await dynamodb.send(getPostCommand);

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        if ((postResult.Item as any).authorId !== authorId) {
            return createResponse(403, { error: 'Not authorized to modify this post' });
        }

        // Update post with media and change status to uploading_media
        const updateCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET mediaId = :mediaId, #status = :status, updatedAt = :updatedAt',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':mediaId': mediaId,
                ':status': 'uploading_media',
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);

        return createResponse(200, {
            message: 'Media attached to post successfully',
            post: result.Attributes
        });

    } catch (error) {
        console.error('AttachMediaToPost error:', error);
        return createResponse(500, { error: 'Failed to attach media to post', details: (error as Error).message });
    }
};

// Publish post (final step)
const publishPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get the post and verify ownership
        const getPostCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const postResult = await dynamodb.send(getPostCommand);

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        if ((postResult.Item as any).authorId !== authorId) {
            return createResponse(403, { error: 'Not authorized to modify this post' });
        }

        // If post has media, verify it's uploaded
        if ((postResult.Item as any).mediaId) {
            const getMediaCommand = new GetCommand({
                TableName: MEDIA_TABLE,
                Key: { id: (postResult.Item as any).mediaId }
            });
            const mediaResult = await dynamodb.send(getMediaCommand);

            // Check if media is approved and ready to be published
            if (!mediaResult.Item || mediaResult.Item.status !== 'approved') {
                return createResponse(400, {
                    error: 'Media upload not completed',
                    details: mediaResult.Item ? `Media status is '${mediaResult.Item.status}'. Media must be approved before publishing posts.` : 'Media not found'
                });
            }
        }

        // Update post to published and active
        const updateCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET #status = :status, active = :active, updatedAt = :updatedAt',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': 'published',
                ':active': true,
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);

        // Fetch media data if post has mediaId
        if ((result.Attributes as any).mediaId) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: (result.Attributes as any).mediaId }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (mediaResult.Item) {
                    (result.Attributes as any).media = mediaResult.Item;
                }
            } catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }

        return createResponse(200, {
            message: 'Post published successfully',
            post: result.Attributes
        });

    } catch (error) {
        console.error('PublishPost error:', error);
        return createResponse(500, { error: 'Failed to publish post', details: (error as Error).message });
    }
};

// Delete post
const deletePost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        const deleteCommand = new DeleteCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        await dynamodb.send(deleteCommand);

        return createResponse(200, { message: 'Post deleted successfully' });

    } catch (error) {
        console.error('DeletePost error:', error);
        return createResponse(500, { error: 'Failed to delete post', details: (error as Error).message });
    }
};

// Like post
const likePost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Check if already liked
        const getLikeCommand = new GetCommand({
            TableName: LIKES_TABLE,
            Key: { postId: id, userId: userId }
        });
        const existingLike = await dynamodb.send(getLikeCommand);

        if (existingLike.Item) {
            return createResponse(400, { error: 'Post already liked' });
        }

        // Add like
        const putLikeCommand = new PutCommand({
            TableName: LIKES_TABLE,
            Item: {
                postId: id,
                userId: userId,
                createdAt: new Date().toISOString()
            }
        });
        await dynamodb.send(putLikeCommand);

        // Update post likes count
        const updatePostCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updatePostCommand);

        // Update user preferences and engagement metrics asynchronously
        updateUserPreferences(userId, id, 'like').catch(error => {
            console.error('Failed to update user preferences:', error);
        });
        updateEngagementMetrics(id).catch(error => {
            console.error('Failed to update engagement metrics:', error);
        });

        return createResponse(200, { message: 'Post liked successfully' });

    } catch (error) {
        console.error('LikePost error:', error);
        return createResponse(500, { error: 'Failed to like post', details: (error as Error).message });
    }
};

// Unlike post
const unlikePost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Remove like
        const deleteLikeCommand = new DeleteCommand({
            TableName: LIKES_TABLE,
            Key: { postId: id, userId: userId }
        });
        await dynamodb.send(deleteLikeCommand);

        // Update post likes count
        const updatePostCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updatePostCommand);

        return createResponse(200, { message: 'Post unliked successfully' });

    } catch (error) {
        console.error('UnlikePost error:', error);
        return createResponse(500, { error: 'Failed to unlike post', details: (error as Error).message });
    }
};

// Get comments for a post
const getComments = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Query comments for the post
        const queryCommand = new QueryCommand({
            TableName: COMMENTS_TABLE,
            IndexName: 'postId-index',
            KeyConditionExpression: 'postId = :postId',
            ExpressionAttributeValues: {
                ':postId': id
            }
        });
        const result = await dynamodb.send(queryCommand);

        // Sort comments by createdAt ascending (oldest first)
        const comments = (result.Items || []).sort((a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

        // Fetch user data for each comment
        const commentsWithUserData = await Promise.all(comments.map(async (comment: any) => {
            try {
                const getUserCommand = new GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: comment.userId }
                });
                const userResult = await dynamodb.send(getUserCommand);

                if (userResult.Item) {
                    comment.username = userResult.Item.username;
                    comment.displayName = userResult.Item.displayName;
                    comment.avatarUrl = userResult.Item.avatarUrl;
                }
            } catch (error) {
                console.error(`Failed to fetch user data for comment ${comment.id}:`, error);
                // Continue without user data if fetch fails
            }
            return comment;
        }));

        return createResponse(200, {
            comments: commentsWithUserData,
            count: commentsWithUserData.length
        });

    } catch (error) {
        console.error('GetComments error:', error);
        return createResponse(500, { error: 'Failed to get comments', details: (error as Error).message });
    }
};

// Create a comment
const createComment = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { content } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!content || content.trim() === '') {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Verify the post exists
        const getPostCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const postResult = await dynamodb.send(getPostCommand);

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        const commentId = uuidv4();
        const comment: any = {
            id: commentId,
            postId: id,
            userId: userId,
            content: content.trim(),
            likeCount: 0,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // Create the comment
        const putCommentCommand = new PutCommand({
            TableName: COMMENTS_TABLE,
            Item: comment
        });
        await dynamodb.send(putCommentCommand);

        // Update post comments count
        const updatePostCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD comments :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updatePostCommand);

        // Update user preferences and engagement metrics asynchronously
        updateUserPreferences(userId, id, 'comment').catch(error => {
            console.error('Failed to update user preferences:', error);
        });
        updateEngagementMetrics(id).catch(error => {
            console.error('Failed to update engagement metrics:', error);
        });

        // Fetch user data for the response
        try {
            const getUserCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userId }
            });
            const userResult = await dynamodb.send(getUserCommand);

            if (userResult.Item) {
                comment.username = userResult.Item.username;
                comment.displayName = userResult.Item.displayName;
                comment.avatarUrl = userResult.Item.avatarUrl;
            }
        } catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }

        return createResponse(201, {
            message: 'Comment created successfully',
            comment
        });

    } catch (error) {
        console.error('CreateComment error:', error);
        return createResponse(500, { error: 'Failed to create comment', details: (error as Error).message });
    }
};

// Update a comment
const updateComment = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Comment ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { content } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!content || content.trim() === '') {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get the comment to verify ownership
        const getCommentCommand = new GetCommand({
            TableName: COMMENTS_TABLE,
            Key: { id }
        });
        const commentResult = await dynamodb.send(getCommentCommand);

        if (!commentResult.Item) {
            return createResponse(404, { error: 'Comment not found' });
        }

        // Verify the user owns the comment
        if ((commentResult.Item as any).userId !== userId) {
            return createResponse(403, { error: 'You can only update your own comments' });
        }

        // Update the comment
        const updateCommand = new UpdateCommand({
            TableName: COMMENTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET content = :content, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':content': content.trim(),
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);

        const updatedComment = result.Attributes as any;

        // Fetch user data for the response
        try {
            const getUserCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userId }
            });
            const userResult = await dynamodb.send(getUserCommand);

            if (userResult.Item) {
                updatedComment.username = userResult.Item.username;
                updatedComment.displayName = userResult.Item.displayName;
                updatedComment.avatarUrl = userResult.Item.avatarUrl;
            }
        } catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }

        return createResponse(200, {
            message: 'Comment updated successfully',
            comment: updatedComment
        });

    } catch (error) {
        console.error('UpdateComment error:', error);
        return createResponse(500, { error: 'Failed to update comment', details: (error as Error).message });
    }
};

// Delete a comment
const deleteComment = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Comment ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get the comment to verify ownership and get post_id
        const getCommentCommand = new GetCommand({
            TableName: COMMENTS_TABLE,
            Key: { id }
        });
        const commentResult = await dynamodb.send(getCommentCommand);

        if (!commentResult.Item) {
            return createResponse(404, { error: 'Comment not found' });
        }

        // Verify the user owns the comment
        if ((commentResult.Item as any).userId !== userId) {
            return createResponse(403, { error: 'You can only delete your own comments' });
        }

        const postId = (commentResult.Item as any).postId;

        // Delete the comment
        const deleteCommentCommand = new DeleteCommand({
            TableName: COMMENTS_TABLE,
            Key: { id }
        });
        await dynamodb.send(deleteCommentCommand);

        // Update post comments count
        const updatePostCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id: postId },
            UpdateExpression: 'ADD comments :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updatePostCommand);

        return createResponse(200, { message: 'Comment deleted successfully' });
    } catch (error) {
        console.error('DeleteComment error:', error);
        return createResponse(500, { error: 'Failed to delete comment', details: (error as Error).message });
    }
};

// Track post view for engagement algorithm
const trackPostView = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};
        const body = JSON.parse(event.body || '{}');
        const { viewDuration, scrollPosition, interacted } = body;

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        const timestamp = new Date().toISOString();
        const viewId = `${userId}#${timestamp}`;

        // Record the view
        const putViewCommand = new PutCommand({
            TableName: POST_VIEWS_TABLE,
            Item: {
                postId: id,
                userId: userId,
                viewId: viewId,
                timestamp: timestamp,
                viewDuration: viewDuration || 0,
                scrollPosition: scrollPosition || 0,
                interacted: interacted || false,
                deviceType: event.headers['User-Agent']?.includes('Mobile') ? 'mobile' : 'desktop'
            }
        });
        await dynamodb.send(putViewCommand);

        // Update engagement metrics asynchronously (don't wait for completion)
        updateEngagementMetrics(id).catch(error => {
            console.error('Failed to update engagement metrics:', error);
        });

        // Update user preferences asynchronously
        updateUserPreferences(userId, id, 'view').catch(error => {
            console.error('Failed to update user preferences:', error);
        });

        return createResponse(200, { message: 'View tracked successfully' });

    } catch (error) {
        console.error('TrackPostView error:', error);
        return createResponse(500, { error: 'Failed to track view', details: (error as Error).message });
    }
};

// Get posts with engagement-based ranking (TikTok-style algorithm)
const getEngagementBasedFeed = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const queryParams = event.queryStringParameters || {};
        const limitParam = queryParams.limit;
        const lastEvaluatedKey = queryParams.lastEvaluatedKey;
        const limitNum = limitParam ? Math.min(parseInt(limitParam), 50) : 10;

        // Get user ID from authorizer context
        const currentUserId = getUserIdFromContext(event);
        if (!currentUserId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get user's viewed posts to exclude them
        const viewedPostsQuery = new QueryCommand({
            TableName: POST_VIEWS_TABLE,
            IndexName: 'userId-timestamp-index',
            KeyConditionExpression: 'userId = :userId',
            ExpressionAttributeValues: { ':userId': currentUserId },
            ProjectionExpression: 'postId',
            ScanIndexForward: false, // Most recent first
            Limit: 1000 // Last 1000 viewed posts
        });
        const viewedResult = await dynamodb.send(viewedPostsQuery);
        const viewedPostIds = new Set((viewedResult.Items || []).map((item: any) => item.postId));

        // Get all published posts
        const scanCommand = new ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: '#status = :status AND #active = :active',
            ExpressionAttributeNames: {
                '#status': 'status',
                '#active': 'active'
            },
            ExpressionAttributeValues: {
                ':status': 'published',
                ':active': true
            },
            ProjectionExpression: 'id, userId, #status, #active, createdAt, title, description, content, mediaId, channelId, likes, comments, reflexes'
        });
        const postsResult = await dynamodb.send(scanCommand);
        const allPosts = postsResult.Items || [];

        // Filter out viewed posts
        const unviewedPosts = allPosts.filter((post: any) => !viewedPostIds.has(post.id));

        // Get engagement metrics for all posts
        const postIds = unviewedPosts.map((post: any) => post.id);
        const engagementMetrics = new Map();

        if (postIds.length > 0) {
            // Batch get engagement metrics
            const batchSize = 100;
            for (let i = 0; i < postIds.length; i += batchSize) {
                const batch = postIds.slice(i, i + batchSize);
                const batchGetCommand = new BatchGetCommand({
                    RequestItems: {
                        [POST_ENGAGEMENT_METRICS_TABLE as string]: {
                            Keys: batch.map(postId => ({ postId })),
                            ProjectionExpression: 'postId, engagementScore, scoreCategory'
                        }
                    }
                });
                const batchResult = await dynamodb.send(batchGetCommand);
                const metricsTableName = POST_ENGAGEMENT_METRICS_TABLE as string;
                if (batchResult.Responses?.[metricsTableName]) {
                    batchResult.Responses[metricsTableName].forEach((metric: any) => {
                        engagementMetrics.set(metric.postId, metric);
                    });
                }
            }
        }

        // Calculate scores for posts without metrics
        const postsWithScores = unviewedPosts.map((post: any) => {
            let metric = engagementMetrics.get(post.id);

            if (!metric) {
                // Calculate basic score for posts without metrics
                const postAge = Date.now() - new Date(post.createdAt).getTime();
                const ageInHours = postAge / (1000 * 60 * 60);
                const timeDecay = Math.exp(-ageInHours / 24);

                const basicScore = (
                    (post.likes || 0) * 3 +
                    (post.comments || 0) * 5 +
                    (post.reflexes || 0) * 4
                ) * timeDecay;

                metric = {
                    engagementScore: basicScore,
                    scoreCategory: basicScore > 20 ? 'rising' : 'normal'
                };
            }

            return {
                ...post,
                engagementScore: metric.engagementScore || 0,
                scoreCategory: metric.scoreCategory || 'normal'
            };
        });

        // Sort by engagement score with some randomization for diversity
        const sortedPosts = postsWithScores.sort((a: any, b: any) => {
            // Add some randomization to prevent always showing the same top posts
            const randomFactorA = 0.8 + Math.random() * 0.4; // 0.8 to 1.2
            const randomFactorB = 0.8 + Math.random() * 0.4;

            const scoreA = a.engagementScore * randomFactorA;
            const scoreB = b.engagementScore * randomFactorB;

            return scoreB - scoreA;
        });

        // Apply pagination
        const paginatedPosts = sortedPosts.slice(0, limitNum);

        console.log(`getEngagementBasedFeed: Found ${allPosts.length} total posts, ${unviewedPosts.length} unviewed, returning ${paginatedPosts.length} posts`);

        // Enrich posts with related data
        const postsWithEnrichedData = await enrichPostsDataOptimized(paginatedPosts, currentUserId);

        return createResponse(200, {
            posts: postsWithEnrichedData,
            hasMore: sortedPosts.length > limitNum,
            algorithm: 'engagement-based'
        });

    } catch (error) {
        console.error('GetEngagementBasedFeed error:', error);
        return createResponse(500, { error: 'Failed to get engagement-based feed', details: (error as Error).message });
    }
};

// Calculate and update engagement metrics for a post
const updateEngagementMetrics = async (postId: string): Promise<void> => {
    try {
        // Get post data
        const getPostCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id: postId }
        });
        const postResult = await dynamodb.send(getPostCommand);
        const post = postResult.Item;

        if (!post) {
            console.error('Post not found for engagement update:', postId);
            return;
        }

        // Get view metrics
        const viewQuery = new QueryCommand({
            TableName: POST_VIEWS_TABLE,
            KeyConditionExpression: 'postId = :postId',
            ExpressionAttributeValues: { ':postId': postId }
        });
        const viewResult = await dynamodb.send(viewQuery);
        const views = viewResult.Items || [];

        // Calculate metrics
        const viewCount = views.length;
        const totalViewDuration = views.reduce((sum: number, view: any) => sum + (view.viewDuration || 0), 0);
        const avgViewDuration = viewCount > 0 ? totalViewDuration / viewCount : 0;
        const interactionRate = viewCount > 0 ? views.filter((view: any) => view.interacted).length / viewCount : 0;

        // Calculate engagement score
        const postAge = Date.now() - new Date(post.createdAt).getTime();
        const ageInHours = postAge / (1000 * 60 * 60);
        const timeDecay = Math.exp(-ageInHours / 24); // Decay over 24 hours

        // Weighted engagement score
        const engagementScore = (
            (post.likes || 0) * 3 +
            (post.comments || 0) * 5 +
            (post.reflexes || 0) * 4 +
            viewCount * 1 +
            avgViewDuration * 0.1 +
            interactionRate * 10
        ) * timeDecay;

        // Determine score category
        let scoreCategory = 'normal';
        if (engagementScore > 100) scoreCategory = 'trending';
        else if (engagementScore > 50) scoreCategory = 'hot';
        else if (engagementScore > 20) scoreCategory = 'rising';

        // Update engagement metrics
        const updateMetricsCommand = new PutCommand({
            TableName: POST_ENGAGEMENT_METRICS_TABLE,
            Item: {
                postId: postId,
                engagementScore: Math.round(engagementScore * 100) / 100,
                scoreCategory: scoreCategory,
                viewCount: viewCount,
                avgViewDuration: Math.round(avgViewDuration * 100) / 100,
                interactionRate: Math.round(interactionRate * 100) / 100,
                lastCalculated: new Date().toISOString(),
                postAge: ageInHours
            }
        });
        await dynamodb.send(updateMetricsCommand);

        console.log(`Updated engagement metrics for post ${postId}: score=${engagementScore}, category=${scoreCategory}`);

    } catch (error) {
        console.error('Error updating engagement metrics:', error);
        throw error;
    }
};

// Update user preferences based on interactions
const updateUserPreferences = async (userId: string, postId: string, interactionType: 'view' | 'like' | 'comment' | 'reaction'): Promise<void> => {
    try {
        // Get post details to extract preferences
        const getPostCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id: postId },
            ProjectionExpression: 'channelId, userId as authorId, createdAt'
        });
        const postResult = await dynamodb.send(getPostCommand);
        const post = postResult.Item;

        if (!post) {
            console.error('Post not found for preference update:', postId);
            return;
        }

        // Get current user preferences
        const getUserPrefsCommand = new GetCommand({
            TableName: USER_PREFERENCES_TABLE,
            Key: { userId }
        });
        const prefsResult = await dynamodb.send(getUserPrefsCommand);
        const currentPrefs = prefsResult.Item || {
            userId,
            preferredChannels: {},
            preferredAuthors: <AUTHORS>
            interactionCounts: { views: 0, likes: 0, comments: 0, reactions: 0 },
            lastUpdated: new Date().toISOString()
        };

        // Update interaction counts
        currentPrefs.interactionCounts[interactionType + 's'] = (currentPrefs.interactionCounts[interactionType + 's'] || 0) + 1;

        // Update channel preferences
        if (post.channelId) {
            const channelScore = currentPrefs.preferredChannels[post.channelId] || 0;
            const interactionWeight = {
                view: 1,
                like: 3,
                comment: 5,
                reaction: 2
            }[interactionType];
            currentPrefs.preferredChannels[post.channelId] = channelScore + interactionWeight;
        }

        // Update author preferences
        if (post.authorId && post.authorId !== userId) { // Don't track self-interactions
            const authorScore = currentPrefs.preferredAuthors[post.authorId] || 0;
            const interactionWeight = {
                view: 1,
                like: 3,
                comment: 5,
                reaction: 2
            }[interactionType];
            currentPrefs.preferredAuthors[post.authorId] = authorScore + interactionWeight;
        }

        currentPrefs.lastUpdated = new Date().toISOString();

        // Save updated preferences
        const updatePrefsCommand = new PutCommand({
            TableName: USER_PREFERENCES_TABLE,
            Item: currentPrefs
        });
        await dynamodb.send(updatePrefsCommand);

        console.log(`Updated user preferences for ${userId}: ${interactionType} on post ${postId}`);

    } catch (error) {
        console.error('Error updating user preferences:', error);
        // Don't throw error to avoid breaking main operations
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        // Posts routes
        if (httpMethod === 'GET' && path === '/posts') {
            return await getPosts(event);
        } else if (httpMethod === 'GET' && path === '/posts/followed') {
            return await getFollowedPosts(event);
        } else if (httpMethod === 'GET' && path === '/posts/engagement-feed') {
            return await getEngagementBasedFeed(event);
        } else if (httpMethod === 'POST' && path === '/posts/draft') {
            return await createDraftPost(event);
        } else if (httpMethod === 'POST' && path === '/posts') {
            return await createPost(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like') && !path.includes('/media') && !path.includes('/publish')) {
            return await getPost(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && path.includes('/media')) {
            return await attachMediaToPost(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && path.includes('/publish')) {
            return await publishPost(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like') && !path.includes('/media') && !path.includes('/publish')) {
            return await updatePost(event);
        } else if (httpMethod === 'DELETE' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like') && !path.includes('/reactions')) {
            return await deletePost(event);
        }
        // Like routes
        else if (httpMethod === 'POST' && path.includes('/like')) {
            return await likePost(event);
        } else if (httpMethod === 'DELETE' && path.includes('/like')) {
            return await unlikePost(event);
        }
        // Reaction routes
        else if (httpMethod === 'POST' && path.includes('/reactions') && pathParameters && pathParameters.id) {
            return await reactToPost(event);
        } else if (httpMethod === 'DELETE' && path.includes('/reactions') && pathParameters && pathParameters.id) {
            return await unreactToPost(event);
        }
        // View tracking route
        else if (httpMethod === 'POST' && path.includes('/view') && pathParameters && pathParameters.id) {
            return await trackPostView(event);
        }
        // Comments routes
        else if (httpMethod === 'GET' && path.includes('/comments') && pathParameters && pathParameters.id) {
            return await getComments(event);
        } else if (httpMethod === 'POST' && path.includes('/comments') && pathParameters && pathParameters.id) {
            return await createComment(event);
        } else if (httpMethod === 'PUT' && path.startsWith('/comments/') && pathParameters && pathParameters.id) {
            return await updateComment(event);
        } else if (httpMethod === 'DELETE' && path.startsWith('/comments/') && pathParameters && pathParameters.id) {
            return await deleteComment(event);
        } else {
            return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: (error as Error).message });
    }
};
