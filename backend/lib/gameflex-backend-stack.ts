import * as cdk from 'aws-cdk-lib';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';

// Import nested stacks
import { DatabaseStack } from './stacks/database-stack';
import { AuthenticationStack } from './stacks/authentication-stack';
import { StorageStack } from './stacks/storage-stack';

// Lambda functions will be created directly in main stack

export interface GameFlexBackendStackProps extends cdk.StackProps {
  environment: string;
  projectName: string;
  domainName?: string;
  mediaDomainName?: string;
  certificateArn?: string;
  mediaCertificateArn?: string;
}

export class GameFlexBackendStack extends cdk.Stack {
  public readonly userPool: cognito.UserPool;
  public readonly userPoolClient: cognito.UserPoolClient;
  public readonly api: apigateway.RestApi;
  public readonly tables: { [key: string]: dynamodb.Table };

  constructor(scope: Construct, id: string, props: GameFlexBackendStackProps) {
    super(scope, id, props);

    const { environment, projectName, domainName, mediaDomainName, certificateArn, mediaCertificateArn } = props;

    // Conditions
    const isProduction = environment === 'production';
    const isStaging = environment === 'staging';
    const isProductionOrStaging = isProduction || isStaging;
    const hasCustomDomain = !!domainName;

    // AWS Secrets Manager for General Configuration
    const appConfigSecretName = `${projectName}-app-config-${environment}`;
    const appConfigSecret = secretsmanager.Secret.fromSecretNameV2(this, 'AppConfigSecret', appConfigSecretName);

    // AWS Secrets Manager for Apple Sign In Configuration
    const appleConfigSecretName = `${projectName}-apple-config-${environment}`;
    const appleConfigSecret = secretsmanager.Secret.fromSecretNameV2(this, 'AppleConfigSecret', appleConfigSecretName);

    // AWS Secrets Manager for Xbox Integration Configuration
    const xboxConfigSecretName = `${projectName}-xbox-config-${environment}`;
    const xboxConfigSecret = secretsmanager.Secret.fromSecretNameV2(this, 'XboxConfigSecret', xboxConfigSecretName);

    // Create nested stacks
    const databaseStack = new DatabaseStack(this, 'DatabaseStack', {
      projectName,
      environment,
      isProductionOrStaging,
    });

    const authStack = new AuthenticationStack(this, 'AuthenticationStack', {
      projectName,
      environment,
      isProductionOrStaging,
    });

    const storageStack = new StorageStack(this, 'StorageStack', {
      projectName,
      environment,
      isProductionOrStaging,
      mediaTable: databaseStack.tables.media,
      usersTable: databaseStack.tables.users,
      mediaDomainName,
      mediaCertificateArn,
    });

    // CDN is now integrated into the Storage stack to avoid circular dependencies



    // Create Lambda functions directly in main stack
    const allFunctions = this.createLambdaFunctions(
      projectName,
      environment,
      authStack.userPool,
      authStack.userPoolClient,
      databaseStack.tables,
      storageStack.mediaBucket,
      storageStack.distribution,
      appConfigSecret,
      appleConfigSecret,
      xboxConfigSecret,
      mediaDomainName
    );

    // Get the authorizer function
    const authorizerFunction = allFunctions.authorizer;

    // Create API Gateway directly in main stack to avoid circular dependencies
    const apiGateway = this.createApiGateway(
      projectName,
      environment,
      isProductionOrStaging,
      hasCustomDomain ? domainName : undefined,
      authorizerFunction,
      allFunctions
    );

    // Lambda permissions are automatically granted by CDK when using LambdaIntegration

    // Set public properties for backward compatibility
    this.userPool = authStack.userPool;
    this.userPoolClient = authStack.userPoolClient;
    this.api = apiGateway.api;
    this.tables = databaseStack.tables;

    // Outputs
    this.createOutputs(environment, domainName, mediaDomainName, appConfigSecret, appleConfigSecret, xboxConfigSecret, apiGateway.apiUrl, storageStack);
  }

  private createLambdaFunctions(
    projectName: string,
    environment: string,
    userPool: cognito.UserPool,
    userPoolClient: cognito.UserPoolClient,
    tables: { [key: string]: dynamodb.Table },
    mediaBucket: any,
    distribution: any,
    appConfigSecret: secretsmanager.ISecret,
    appleConfigSecret: secretsmanager.ISecret,
    xboxConfigSecret: secretsmanager.ISecret,
    mediaDomainName?: string
  ): { [key: string]: lambda.Function } {
    const functions: { [key: string]: lambda.Function } = {};

    // Common environment variables
    const commonEnv = {
      ENVIRONMENT: environment,
      USER_POOL_ID: userPool.userPoolId,
      USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      USERS_TABLE: tables.users.tableName,
      POSTS_TABLE: tables.posts.tableName,
      MEDIA_TABLE: tables.media.tableName,
      CHANNELS_TABLE: tables.channels.tableName,
      REFLEXES_TABLE: tables.reflexes.tableName,
      REFLEX_LIKES_TABLE: tables.reflexLikes.tableName,
      COMMENTS_TABLE: tables.comments.tableName,
      LIKES_TABLE: tables.likes.tableName,
      POST_REACTIONS_TABLE: tables.postReactions.tableName,
      REFLEX_REACTIONS_TABLE: tables.reflexReactions.tableName,
      USER_PROFILES_TABLE: tables.userProfiles.tableName,
      FOLLOWS_TABLE: tables.follows.tableName,
      CHANNEL_MEMBERS_TABLE: tables.channelMembers.tableName,
      XBOX_ACCOUNTS_TABLE: tables.xboxAccounts.tableName,
      POST_VIEWS_TABLE: tables.postViews.tableName,
      POST_ENGAGEMENT_METRICS_TABLE: tables.postEngagementMetrics.tableName,
      USER_PREFERENCES_TABLE: tables.userPreferences.tableName,
      APP_CONFIG_SECRET_ARN: appConfigSecret.secretArn,
      APP_CONFIG_SECRET_NAME: appConfigSecret.secretName,
      APPLE_CONFIG_SECRET_ARN: appleConfigSecret.secretArn,
      XBOX_CONFIG_SECRET_ARN: xboxConfigSecret.secretArn,
      XBOX_CONFIG_SECRET_NAME: xboxConfigSecret.secretName,
    };

    // Create authorizer function
    functions.authorizer = new lambda.Function(this, 'AuthorizerFunction', {
      runtime: lambda.Runtime.NODEJS_22_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/authorizer'),
      environment: commonEnv,
      timeout: cdk.Duration.seconds(30),
    });

    // Create API functions (removed 'auth' to avoid confusion with specific auth functions)
    const apiFunctions = ['posts', 'users', 'channels', 'reflexes', 'health'];
    apiFunctions.forEach(funcName => {
      functions[funcName] = new lambda.Function(this, `${funcName.charAt(0).toUpperCase() + funcName.slice(1)}Function`, {
        runtime: lambda.Runtime.NODEJS_22_X,
        handler: 'index.handler',
        code: lambda.Code.fromAsset(`src/${funcName}`),
        environment: commonEnv,
        timeout: cdk.Duration.seconds(30),
      });
    });

    // Create media function with special environment variables
    functions.media = new lambda.Function(this, 'MediaFunction', {
      runtime: lambda.Runtime.NODEJS_22_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/media'),
      environment: {
        ...commonEnv,
        MEDIA_BUCKET: mediaBucket.bucketName,
        CLOUDFRONT_DOMAIN: mediaDomainName || distribution?.distributionDomainName || 'localhost',
      },
      timeout: cdk.Duration.seconds(30),
    });

    // Create specific auth functions with correct handlers
    const authFunctionMappings = {
      'authSignup': 'signup.handler',
      'authSignin': 'signin.handler',
      'authAppleSignin': 'apple-signin.handler',
      'authRefresh': 'refresh.handler',
      'authValidate': 'validate.handler',
      'authSetUsername': 'set-username.handler',
      'authCheckUsernameAvailability': 'check-username-availability.handler',
    };

    Object.entries(authFunctionMappings).forEach(([funcName, handler]) => {
      functions[funcName] = new lambda.Function(this, `${funcName}Function`, {
        runtime: lambda.Runtime.NODEJS_22_X,
        handler: handler,
        code: lambda.Code.fromAsset('src/auth'),
        environment: commonEnv,
        timeout: cdk.Duration.seconds(30),
      });
    });

    // Create Xbox functions
    functions.xbox = new lambda.Function(this, 'XboxFunction', {
      runtime: lambda.Runtime.NODEJS_22_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/xbox'),
      environment: commonEnv,
      timeout: cdk.Duration.seconds(30),
    });

    functions.xboxMedia = new lambda.Function(this, 'XboxMediaFunction', {
      runtime: lambda.Runtime.NODEJS_22_X,
      handler: 'media.handler',
      code: lambda.Code.fromAsset('src/xbox'),
      environment: commonEnv,
      timeout: cdk.Duration.seconds(30),
    });

    // Grant permissions to tables
    Object.values(tables).forEach(table => {
      Object.values(functions).forEach(func => {
        table.grantReadWriteData(func);
      });
    });

    // Grant permissions to secrets
    [appConfigSecret, appleConfigSecret, xboxConfigSecret].forEach(secret => {
      Object.values(functions).forEach(func => {
        secret.grantRead(func);
      });
    });

    // Grant Cognito permissions to auth functions and Xbox function
    const authFunctionNames = ['authSignup', 'authSignin', 'authAppleSignin', 'authRefresh', 'authValidate', 'authSetUsername', 'authCheckUsernameAvailability', 'xbox'];
    authFunctionNames.forEach(funcName => {
      if (functions[funcName]) {
        functions[funcName].addToRolePolicy(new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'cognito-idp:AdminInitiateAuth',
            'cognito-idp:AdminCreateUser',
            'cognito-idp:AdminSetUserPassword',
            'cognito-idp:AdminGetUser',
            'cognito-idp:AdminUpdateUserAttributes',
            'cognito-idp:AdminDeleteUser',
            'cognito-idp:ListUsers',
            'cognito-idp:AdminConfirmSignUp',
            'cognito-idp:AdminRespondToAuthChallenge',
            'cognito-idp:GetUser',
            'cognito-idp:ChangePassword',
            'cognito-idp:ConfirmForgotPassword',
            'cognito-idp:ForgotPassword',
            'cognito-idp:RespondToAuthChallenge',
            'cognito-idp:InitiateAuth',
          ],
          resources: [userPool.userPoolArn],
        }));
      }
    });

    // Grant Cognito permissions to authorizer function
    if (functions.authorizer) {
      functions.authorizer.addToRolePolicy(new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'cognito-idp:GetUser',
          'cognito-idp:AdminGetUser',
        ],
        resources: [userPool.userPoolArn],
      }));
    }

    return functions;
  }
  private createApiGateway(
    projectName: string,
    environment: string,
    isProductionOrStaging: boolean,
    domainName: string | undefined,
    authorizerFunction: lambda.Function | undefined,
    allFunctions: { [key: string]: lambda.Function }
  ): { api: apigateway.RestApi; apiUrl: string } {
    // Create API Gateway directly in main stack
    const api = new apigateway.RestApi(this, 'Api', {
      restApiName: `${projectName}-${environment}`,
      description: `GameFlex API for ${environment} environment`,
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: ['Content-Type', 'X-Amz-Date', 'Authorization', 'X-Api-Key'],
      },
      deployOptions: {
        stageName: environment,
        throttlingRateLimit: isProductionOrStaging ? 1000 : 100,
        throttlingBurstLimit: isProductionOrStaging ? 2000 : 200,
      },
    });

    // Create authorizer if function is provided
    let authorizer: apigateway.RequestAuthorizer | undefined;
    if (authorizerFunction) {
      authorizer = new apigateway.RequestAuthorizer(this, 'Authorizer', {
        handler: authorizerFunction,
        identitySources: [apigateway.IdentitySource.header('Authorization')],
        resultsCacheTtl: cdk.Duration.minutes(5),
      });
    }

    // Create API resources with proper Lambda integrations
    this.createApiResources(api, authorizer, allFunctions);

    // Attach to existing custom domain if provided
    let apiUrl = api.url;
    if (domainName) {
      // Import the existing custom domain
      const customDomain = apigateway.DomainName.fromDomainNameAttributes(this, 'ApiCustomDomain', {
        domainName: domainName,
        domainNameAliasHostedZoneId: 'Z2FDTNDATAQYW2', // CloudFront hosted zone ID for edge-optimized domains
        domainNameAliasTarget: `${domainName}`, // This will be resolved by AWS
      });

      // Create the base path mapping to attach this API to the existing domain
      new apigateway.BasePathMapping(this, 'ApiBasePathMapping', {
        domainName: customDomain,
        restApi: api,
        stage: api.deploymentStage,
      });

      // Use the custom domain URL
      apiUrl = `https://${domainName}/`;
    }

    return { api, apiUrl };
  }
  private createApiResources(
    api: apigateway.RestApi,
    authorizer: apigateway.RequestAuthorizer | undefined,
    allFunctions: { [key: string]: lambda.Function }
  ): void {
    // Helper function to create Lambda integration
    const createLambdaIntegration = (functionName: string): apigateway.LambdaIntegration => {
      if (!allFunctions[functionName]) {
        throw new Error(`Lambda function '${functionName}' not found`);
      }
      return new apigateway.LambdaIntegration(allFunctions[functionName], { proxy: true });
    };

    // Helper function to create method options
    const createMethodOptions = (requireAuth: boolean = true): apigateway.MethodOptions => {
      const options: apigateway.MethodOptions = {
        methodResponses: [
          { statusCode: '200' },
          { statusCode: '400' },
          { statusCode: '401' },
          { statusCode: '403' },
          { statusCode: '500' },
        ],
      };
      if (requireAuth && authorizer) {
        (options as any).authorizer = authorizer;
      }
      return options;
    };

    // Health endpoint (public)
    const healthResource = api.root.addResource('health');
    healthResource.addMethod('GET', createLambdaIntegration('health'), createMethodOptions(false));

    // Auth endpoints
    const authResource = api.root.addResource('auth');

    // Public auth endpoints
    authResource.addResource('signup').addMethod('POST', createLambdaIntegration('authSignup'), createMethodOptions(false));
    authResource.addResource('signin').addMethod('POST', createLambdaIntegration('authSignin'), createMethodOptions(false));
    authResource.addResource('apple-signin').addMethod('POST', createLambdaIntegration('authAppleSignin'), createMethodOptions(false));
    authResource.addResource('apple').addMethod('POST', createLambdaIntegration('authAppleSignin'), createMethodOptions(false)); // Alternative route for Apple sign in
    authResource.addResource('refresh').addMethod('POST', createLambdaIntegration('authRefresh'), createMethodOptions(false));

    // Protected auth endpoints
    authResource.addResource('validate').addMethod('GET', createLambdaIntegration('authValidate'), createMethodOptions(true));
    authResource.addResource('set-username').addMethod('POST', createLambdaIntegration('authSetUsername'), createMethodOptions(true));
    authResource.addResource('check-username-availability').addMethod('GET', createLambdaIntegration('authSetUsername'), createMethodOptions(true));

    // Posts endpoints (all protected) - INCLUDING THE MEDIA ATTACHMENT ENDPOINT
    const postsResource = api.root.addResource('posts');
    postsResource.addMethod('GET', createLambdaIntegration('posts'), createMethodOptions(true));
    postsResource.addMethod('POST', createLambdaIntegration('posts'), createMethodOptions(true));

    postsResource.addResource('draft').addMethod('POST', createLambdaIntegration('posts'), createMethodOptions(true));
    postsResource.addResource('followed').addMethod('GET', createLambdaIntegration('posts'), createMethodOptions(true));
    postsResource.addResource('engagement-feed').addMethod('GET', createLambdaIntegration('posts'), createMethodOptions(true));

    const postResource = postsResource.addResource('{id}');
    postResource.addMethod('GET', createLambdaIntegration('posts'), createMethodOptions(true));
    postResource.addMethod('DELETE', createLambdaIntegration('posts'), createMethodOptions(true));
    postResource.addResource('media').addMethod('PUT', createLambdaIntegration('posts'), createMethodOptions(true));
    postResource.addResource('publish').addMethod('PUT', createLambdaIntegration('posts'), createMethodOptions(true));
    postResource.addResource('view').addMethod('POST', createLambdaIntegration('posts'), createMethodOptions(true));
    const likeResource = postResource.addResource('like');
    likeResource.addMethod('POST', createLambdaIntegration('posts'), createMethodOptions(true));
    likeResource.addMethod('DELETE', createLambdaIntegration('posts'), createMethodOptions(true));
    const reactionsResource = postResource.addResource('reactions');
    reactionsResource.addMethod('POST', createLambdaIntegration('posts'), createMethodOptions(true));
    reactionsResource.addMethod('DELETE', createLambdaIntegration('posts'), createMethodOptions(true));
    const commentsResource = postResource.addResource('comments');
    commentsResource.addMethod('GET', createLambdaIntegration('posts'), createMethodOptions(true));
    commentsResource.addMethod('POST', createLambdaIntegration('posts'), createMethodOptions(true));

    // Post reflexes endpoints
    const postReflexesResource = postResource.addResource('reflexes');
    postReflexesResource.addMethod('GET', createLambdaIntegration('reflexes'), createMethodOptions(true));
    postReflexesResource.addMethod('POST', createLambdaIntegration('reflexes'), createMethodOptions(true));

    // Media endpoints (all protected)
    const mediaResource = api.root.addResource('media');
    mediaResource.addResource('upload').addMethod('POST', createLambdaIntegration('media'), createMethodOptions(true));

    const mediaItemResource = mediaResource.addResource('{id}');
    mediaItemResource.addMethod('GET', createLambdaIntegration('media'), createMethodOptions(true));
    mediaItemResource.addMethod('PUT', createLambdaIntegration('media'), createMethodOptions(true));
    mediaItemResource.addMethod('DELETE', createLambdaIntegration('media'), createMethodOptions(true));
    mediaItemResource.addResource('process').addMethod('POST', createLambdaIntegration('media'), createMethodOptions(true));
    mediaItemResource.addResource('analysis').addMethod('GET', createLambdaIntegration('media'), createMethodOptions(true));
    mediaItemResource.addResource('review').addMethod('GET', createLambdaIntegration('media'), createMethodOptions(true));
    mediaItemResource.addResource('approve').addMethod('POST', createLambdaIntegration('media'), createMethodOptions(true));

    // Users endpoints (all protected)
    const usersResource = api.root.addResource('users');
    usersResource.addMethod('GET', createLambdaIntegration('users'), createMethodOptions(true));

    // User profile endpoints
    const profileResource = usersResource.addResource('profile');
    profileResource.addMethod('GET', createLambdaIntegration('users'), createMethodOptions(true));
    profileResource.addMethod('PUT', createLambdaIntegration('users'), createMethodOptions(true));
    profileResource.addResource('liked-posts').addMethod('GET', createLambdaIntegration('users'), createMethodOptions(true));

    const userResource = usersResource.addResource('{id}');
    userResource.addMethod('GET', createLambdaIntegration('users'), createMethodOptions(true));

    // User posts endpoint
    userResource.addResource('posts').addMethod('GET', createLambdaIntegration('users'), createMethodOptions(true));

    // User follow/unfollow endpoints
    const userFollowResource = userResource.addResource('follow');
    userFollowResource.addMethod('POST', createLambdaIntegration('users'), createMethodOptions(true));
    userFollowResource.addMethod('DELETE', createLambdaIntegration('users'), createMethodOptions(true));

    // Channels endpoints (all protected)
    const channelsResource = api.root.addResource('channels');
    channelsResource.addMethod('GET', createLambdaIntegration('channels'), createMethodOptions(true));
    channelsResource.addMethod('POST', createLambdaIntegration('channels'), createMethodOptions(true));

    // Reflexes endpoints (all protected)
    const reflexesResource = api.root.addResource('reflexes');
    reflexesResource.addMethod('GET', createLambdaIntegration('reflexes'), createMethodOptions(true));
    reflexesResource.addMethod('POST', createLambdaIntegration('reflexes'), createMethodOptions(true));

    // Individual reflex endpoints
    const reflexResource = reflexesResource.addResource('{id}');
    reflexResource.addMethod('DELETE', createLambdaIntegration('reflexes'), createMethodOptions(true));

    // Reflex like endpoints
    const reflexLikeResource = reflexResource.addResource('like');
    reflexLikeResource.addMethod('POST', createLambdaIntegration('reflexes'), createMethodOptions(true));
    reflexLikeResource.addMethod('DELETE', createLambdaIntegration('reflexes'), createMethodOptions(true));

    // Reflex reactions endpoints
    const reflexReactionsResource = reflexResource.addResource('reactions');
    reflexReactionsResource.addMethod('POST', createLambdaIntegration('reflexes'), createMethodOptions(true));
    reflexReactionsResource.addMethod('DELETE', createLambdaIntegration('reflexes'), createMethodOptions(true));

    // Xbox endpoints
    const xboxResource = api.root.addResource('xbox');

    // Xbox auth endpoints (some public, some protected)
    xboxResource.addResource('auth').addMethod('GET', createLambdaIntegration('xbox'), createMethodOptions(false)); // Public for starting auth flow
    xboxResource.addResource('callback').addMethod('GET', createLambdaIntegration('xbox'), createMethodOptions(false)); // Public for OAuth callback
    xboxResource.addResource('signin').addMethod('POST', createLambdaIntegration('xbox'), createMethodOptions(false)); // Public for sign in
    xboxResource.addResource('link').addMethod('POST', createLambdaIntegration('xbox'), createMethodOptions(true));

    // Xbox account endpoints
    const xboxAccountResource = xboxResource.addResource('account');
    xboxAccountResource.addMethod('GET', createLambdaIntegration('xbox'), createMethodOptions(true));
    xboxAccountResource.addMethod('DELETE', createLambdaIntegration('xbox'), createMethodOptions(true));

    xboxResource.addResource('relink-account').addMethod('POST', createLambdaIntegration('xbox'), createMethodOptions(true));
    xboxResource.addResource('create-new-account').addMethod('POST', createLambdaIntegration('xbox'), createMethodOptions(true));

    // Xbox media endpoints (all protected)
    xboxResource.addResource('screenshots').addMethod('GET', createLambdaIntegration('xboxMedia'), createMethodOptions(true));
    xboxResource.addResource('gameclips').addMethod('GET', createLambdaIntegration('xboxMedia'), createMethodOptions(true));
  }





  private createOutputs(
    environment: string,
    domainName?: string,
    mediaDomainName?: string,
    appConfigSecret?: secretsmanager.ISecret,
    appleConfigSecret?: secretsmanager.ISecret,
    xboxConfigSecret?: secretsmanager.ISecret,
    apiUrl?: string,
    storageStack?: StorageStack
  ): void {
    // API Gateway URL
    new cdk.CfnOutput(this, 'ApiGatewayUrl', {
      value: apiUrl || this.api.url,
      description: 'API Gateway URL',
      exportName: `${this.stackName}-ApiGatewayUrl`,
    });

    // API Documentation URL (OpenAPI/Swagger export)
    new cdk.CfnOutput(this, 'ApiDocumentationUrl', {
      value: `https://${this.api.restApiId}.execute-api.${this.region}.amazonaws.com/v1/_doc`,
      description: 'API Documentation URL (OpenAPI/Swagger export)',
      exportName: `${this.stackName}-ApiDocumentationUrl`,
    });

    // User Pool ID
    new cdk.CfnOutput(this, 'UserPoolId', {
      value: this.userPool.userPoolId,
      description: 'Cognito User Pool ID',
      exportName: `${this.stackName}-UserPoolId`,
    });

    // User Pool Client ID
    new cdk.CfnOutput(this, 'UserPoolClientId', {
      value: this.userPoolClient.userPoolClientId,
      description: 'Cognito User Pool Client ID',
      exportName: `${this.stackName}-UserPoolClientId`,
    });

    // App Config Secret ARN
    if (appConfigSecret) {
      new cdk.CfnOutput(this, 'AppConfigSecretArn', {
        value: appConfigSecret.secretArn,
        description: 'App Configuration Secret ARN',
        exportName: `${this.stackName}-AppConfigSecretArn`,
      });
    }

    // Apple Config Secret ARN
    if (appleConfigSecret) {
      new cdk.CfnOutput(this, 'AppleConfigSecretArn', {
        value: appleConfigSecret.secretArn,
        description: 'Apple Sign In Configuration Secret ARN',
        exportName: `${this.stackName}-AppleConfigSecretArn`,
      });
    }

    // Xbox Config Secret ARN
    if (xboxConfigSecret) {
      new cdk.CfnOutput(this, 'XboxConfigSecretArn', {
        value: xboxConfigSecret.secretArn,
        description: 'Xbox Integration Configuration Secret ARN',
        exportName: `${this.stackName}-XboxConfigSecretArn`,
      });
    }

    // DynamoDB Table Names
    Object.entries(this.tables).forEach(([name, table]) => {
      new cdk.CfnOutput(this, `${name.charAt(0).toUpperCase() + name.slice(1)}TableName`, {
        value: table.tableName,
        description: `${name} DynamoDB Table Name`,
        exportName: `${this.stackName}-${name.charAt(0).toUpperCase() + name.slice(1)}TableName`,
      });
    });

    // S3 Bucket outputs
    if (storageStack) {
      new cdk.CfnOutput(this, 'MediaBucketName', {
        value: storageStack.bucketName,
        description: 'Name of the S3 media bucket',
        exportName: `${this.stackName}-MediaBucketName`,
      });

      new cdk.CfnOutput(this, 'MediaBucketArn', {
        value: storageStack.bucketArn,
        description: 'ARN of the S3 media bucket',
        exportName: `${this.stackName}-MediaBucketArn`,
      });
    }

    // CloudFront Distribution outputs
    if (storageStack?.distribution) {
      new cdk.CfnOutput(this, 'CloudFrontDistributionDomain', {
        value: storageStack.distribution.distributionDomainName,
        description: 'CloudFront Distribution Domain Name',
        exportName: `${this.stackName}-CloudFrontDomain`,
      });

      new cdk.CfnOutput(this, 'CloudFrontDistributionUrl', {
        value: `https://${storageStack.distribution.distributionDomainName}`,
        description: 'CloudFront Distribution URL',
        exportName: `${this.stackName}-CloudFrontUrl`,
      });

      // Media custom domain outputs if configured
      if (mediaDomainName) {
        new cdk.CfnOutput(this, 'MediaCustomDomain', {
          value: mediaDomainName,
          description: 'Custom domain name for media CDN',
          exportName: `${this.stackName}-MediaCustomDomain`,
        });

        new cdk.CfnOutput(this, 'MediaCustomDomainUrl', {
          value: `https://${mediaDomainName}`,
          description: 'Media CDN URL with custom domain',
          exportName: `${this.stackName}-MediaCustomDomainUrl`,
        });
      }
    }
  }
}
